{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/component.ts", "../../src/constants.ts", "../../src/provider.ts", "../../src/component_container.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n"], "names": [], "mappings": ";;AAyBA;;AAEG;MACU,SAAS,CAAA;AAWpB;;;;;AAKG;AACH,IAAA,WAAA,CACW,IAAO,EACP,eAAmC,EACnC,IAAmB,EAAA;QAFnB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;QACP,IAAe,CAAA,eAAA,GAAf,eAAe,CAAoB;QACnC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;QAnB9B,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;AAC1B;;AAEG;QACH,IAAY,CAAA,YAAA,GAAe,EAAE,CAAC;AAE9B,QAAA,IAAA,CAAA,iBAAiB,GAA0B,MAAA,8BAAA;QAE3C,IAAiB,CAAA,iBAAA,GAAwC,IAAI,CAAC;KAY1D;AAEJ,IAAA,oBAAoB,CAAC,IAAuB,EAAA;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,oBAAoB,CAAC,iBAA0B,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,eAAe,CAAC,KAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,0BAA0B,CAAC,QAAsC,EAAA;AAC/D,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AAClC,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;ACtED;;;;;;;;;;;;;;;AAeG;AAEI,MAAM,kBAAkB,GAAG,WAAW;;ACjB7C;;;;;;;;;;;;;;;AAeG;AAcH;;;AAGG;MACU,QAAQ,CAAA;IAWnB,WACmB,CAAA,IAAO,EACP,SAA6B,EAAA;QAD7B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;QACP,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;QAZxC,IAAS,CAAA,SAAA,GAAwB,IAAI,CAAC;AAC7B,QAAA,IAAA,CAAA,SAAS,GAAuC,IAAI,GAAG,EAAE,CAAC;AAC1D,QAAA,IAAA,CAAA,iBAAiB,GAG9B,IAAI,GAAG,EAAE,CAAC;AACG,QAAA,IAAA,CAAA,gBAAgB,GAC/B,IAAI,GAAG,EAAE,CAAC;AACJ,QAAA,IAAA,CAAA,eAAe,GAAwC,IAAI,GAAG,EAAE,CAAC;KAKrE;AAEJ;;;AAGG;AACH,IAAA,GAAG,CAAC,UAAmB,EAAA;;QAErB,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;AACrD,YAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAyB,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AAE3D,YAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;AACxC,gBAAA,IAAI,CAAC,oBAAoB,EAAE,EAC3B;;AAEA,gBAAA,IAAI;AACF,oBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,wBAAA,kBAAkB,EAAE,oBAAoB;AACzC,qBAAA,CAAC,CAAC;oBACH,IAAI,QAAQ,EAAE;AACZ,wBAAA,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;qBAC5B;iBACF;gBAAC,OAAO,CAAC,EAAE;;;iBAGX;aACF;SACF;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAE,CAAC,OAAO,CAAC;KAClE;AAkBD,IAAA,YAAY,CAAC,OAGZ,EAAA;;;AAEC,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,UAAU,CACpB,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;AAE5C,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;AACxC,YAAA,IAAI,CAAC,oBAAoB,EAAE,EAC3B;AACA,YAAA,IAAI;gBACF,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACjC,oBAAA,kBAAkB,EAAE,oBAAoB;AACzC,iBAAA,CAAC,CAAC;aACJ;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,QAAQ,EAAE;AACZ,oBAAA,OAAO,IAAI,CAAC;iBACb;qBAAM;AACL,oBAAA,MAAM,CAAC,CAAC;iBACT;aACF;SACF;aAAM;;YAEL,IAAI,QAAQ,EAAE;AACZ,gBAAA,OAAO,IAAI,CAAC;aACb;iBAAM;gBACL,MAAM,KAAK,CAAC,CAAW,QAAA,EAAA,IAAI,CAAC,IAAI,CAAA,iBAAA,CAAmB,CAAC,CAAC;aACtD;SACF;KACF;IAED,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAED,IAAA,YAAY,CAAC,SAAuB,EAAA;QAClC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AAChC,YAAA,MAAM,KAAK,CACT,CAAyB,sBAAA,EAAA,SAAS,CAAC,IAAI,CAAiB,cAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,CACrE,CAAC;SACH;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,KAAK,CAAC,CAAiB,cAAA,EAAA,IAAI,CAAC,IAAI,CAAA,0BAAA,CAA4B,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;AAG3B,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAChC,OAAO;SACR;;AAGD,QAAA,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;AAC/B,YAAA,IAAI;gBACF,IAAI,CAAC,sBAAsB,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,CAAC;aACzE;YAAC,OAAO,CAAC,EAAE;;;;;aAKX;SACF;;;;AAKD,QAAA,KAAK,MAAM,CACT,kBAAkB,EAClB,gBAAgB,CACjB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE;YACrC,MAAM,oBAAoB,GACxB,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;AAEvD,YAAA,IAAI;;AAEF,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,oBAAA,kBAAkB,EAAE,oBAAoB;AACzC,iBAAA,CAAE,CAAC;AACJ,gBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACpC;YAAC,OAAO,CAAC,EAAE;;;aAGX;SACF;KACF;IAED,aAAa,CAAC,aAAqB,kBAAkB,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACnC;;;AAID,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAErD,MAAM,OAAO,CAAC,GAAG,CAAC;AAChB,YAAA,GAAG,QAAQ;iBACR,MAAM,CAAC,OAAO,IAAI,UAAU,IAAI,OAAO,CAAC;;iBAExC,GAAG,CAAC,OAAO,IAAK,OAAe,CAAC,QAAS,CAAC,MAAM,EAAE,CAAC;AACtD,YAAA,GAAG,QAAQ;iBACR,MAAM,CAAC,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC;;iBAEvC,GAAG,CAAC,OAAO,IAAK,OAAe,CAAC,OAAO,EAAE,CAAC;AAC9C,SAAA,CAAC,CAAC;KACJ;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;KAC/B;IAED,aAAa,CAAC,aAAqB,kBAAkB,EAAA;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACvC;IAED,UAAU,CAAC,aAAqB,kBAAkB,EAAA;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;KACpD;IAED,UAAU,CAAC,OAA0B,EAAE,EAAA;AACrC,QAAA,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QAC9B,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,IAAI,CAAC,kBAAkB,CACxB,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE;YAC5C,MAAM,KAAK,CACT,CAAA,EAAG,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,oBAAoB,CAAgC,8BAAA,CAAA,CACrE,CAAC;SACH;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,KAAK,CAAC,CAAa,UAAA,EAAA,IAAI,CAAC,IAAI,CAAA,4BAAA,CAA8B,CAAC,CAAC;SACnE;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,YAAA,kBAAkB,EAAE,oBAAoB;YACxC,OAAO;AACR,SAAA,CAAE,CAAC;;AAGJ,QAAA,KAAK,MAAM,CACT,kBAAkB,EAClB,gBAAgB,CACjB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE;YACrC,MAAM,4BAA4B,GAChC,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;AACvD,YAAA,IAAI,oBAAoB,KAAK,4BAA4B,EAAE;AACzD,gBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACpC;SACF;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED;;;;;;;AAOG;IACH,MAAM,CAAC,QAA2B,EAAE,UAAmB,EAAA;;QACrD,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;AAC1E,QAAA,MAAM,iBAAiB,GACrB,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAC9C,IAAI,GAAG,EAAqB,CAAC;AAC/B,QAAA,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;QAElE,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE;AACpB,YAAA,QAAQ,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;SAClD;AAED,QAAA,OAAO,MAAK;AACV,YAAA,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAC,CAAC;KACH;AAED;;;AAGG;IACK,qBAAqB,CAC3B,QAA+B,EAC/B,UAAkB,EAAA;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;AACD,QAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,YAAA,IAAI;AACF,gBAAA,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;aAChC;AAAC,YAAA,OAAA,EAAA,EAAM;;aAEP;SACF;KACF;AAEO,IAAA,sBAAsB,CAAC,EAC7B,kBAAkB,EAClB,OAAO,GAAG,EAAE,EAIb,EAAA;QACC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;AACxD,gBAAA,kBAAkB,EAAE,6BAA6B,CAAC,kBAAkB,CAAC;gBACrE,OAAO;AACR,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAS,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAEvD;;;;AAIG;AACH,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAS,EAAE,kBAAkB,CAAC,CAAC;AAE1D;;;;AAIG;AACH,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;AACpC,gBAAA,IAAI;AACF,oBAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,EACd,kBAAkB,EAClB,QAAS,CACV,CAAC;iBACH;AAAC,gBAAA,OAAA,EAAA,EAAM;;iBAEP;aACF;SACF;QAED,OAAO,QAAQ,IAAI,IAAI,CAAC;KACzB;IAEO,2BAA2B,CACjC,aAAqB,kBAAkB,EAAA;AAEvC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAG,kBAAkB,CAAC;SAC3E;aAAM;YACL,OAAO,UAAU,CAAC;SACnB;KACF;IAEO,oBAAoB,GAAA;AAC1B,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,SAAS;AAChB,YAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,KAAA,UAAA,mCAChC;KACH;AACF,CAAA;AAED;AACA,SAAS,6BAA6B,CAAC,UAAkB,EAAA;IACvD,OAAO,UAAU,KAAK,kBAAkB,GAAG,SAAS,GAAG,UAAU,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAiB,SAAuB,EAAA;AAC/D,IAAA,OAAO,SAAS,CAAC,iBAAiB,KAAA,OAAA,+BAA6B;AACjE;;ACzXA;;;;;;;;;;;;;;;AAeG;AAMH;;AAEG;MACU,kBAAkB,CAAA;AAG7B,IAAA,WAAA,CAA6B,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAFxB,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;KAElB;AAE7C;;;;;;;;AAQG;AACH,IAAA,YAAY,CAAiB,SAAuB,EAAA;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;AAC7B,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,UAAA,EAAa,SAAS,CAAC,IAAI,CAAA,kCAAA,EAAqC,IAAI,CAAC,IAAI,CAAA,CAAE,CAC5E,CAAC;SACH;AAED,QAAA,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAClC;AAED,IAAA,uBAAuB,CAAiB,SAAuB,EAAA;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;;YAE7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACvC;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC9B;AAED;;;;;;AAMG;AACH,IAAA,WAAW,CAAiB,IAAO,EAAA;QACjC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAA2B,CAAC;SAC3D;;QAGD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAqC,CAAC,CAAC;AAEhE,QAAA,OAAO,QAAuB,CAAC;KAChC;IAED,YAAY,GAAA;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;KAC5C;AACF;;;;"}